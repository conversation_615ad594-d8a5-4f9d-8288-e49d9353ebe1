// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

interface IBDIORegistry {
    function getDocumentOwner(string calldata bdioId) external view returns (address);
}

contract VCManager {
    IBDIORegistry public bdioRegistry;
    mapping(string => string) public vcHashes;

    event VCHashSet(string indexed bdioId, string vcHash);

    constructor(address registryAddress) {
        bdioRegistry = IBDIORegistry(registryAddress);
    }

    function setVCHash(string calldata bdioId, string calldata newVCHash) external {
        require(msg.sender == bdioRegistry.getDocumentOwner(bdioId), "Only owner can set VC hash");
        vcHashes[bdioId] = newVCHash;
        emit VCHashSet(bdioId, newVCHash);
    }

    function vcHash(string calldata bdioId) external view returns (string memory) {
        return vcHashes[bdioId];
    }
}
