// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

/// @title BDIO Expiry Manager
/// @notice Manage expiry date of documents registered in BDIORegistry
interface IBDIORegistry {
    function getDocumentOwner(string calldata bdioId) external view returns (address);
}

contract BDIOExpiryManager {
    IBDIORegistry public bdioRegistry;

    /// @dev Mapping: bdioId => expiry timestamp (0 = no expiry)
    mapping(string => uint256) public expiry;

    /// @notice Emitted when expiry is set or cleared
    event ExpirySet(string indexed bdioId, uint256 timestamp);

    constructor(address registryAddress) {
        bdioRegistry = IBDIORegistry(registryAddress);
    }

    /// @notice Set expiry for a single document
    /// @param bdioId Document ID
    /// @param expiryTimestamp Unix timestamp (0 = no expiry)
    function setExpiry(string calldata bdioId, uint256 expiryTimestamp) external {
        require(msg.sender == bdioRegistry.getDocumentOwner(bdioId), "Only owner can set expiry");
        require(expiryTimestamp == 0 || expiryTimestamp > block.timestamp, "Invalid expiry");
        expiry[bdioId] = expiryTimestamp;
        emit ExpirySet(bdioId, expiryTimestamp);
    }

    /// @notice Set expiry for multiple documents in batch
    /// @param bdioIds List of document IDs
    /// @param timestamps List of expiry timestamps (0 = no expiry)
    function batchSetExpiry(string[] calldata bdioIds, uint256[] calldata timestamps) external {
        require(bdioIds.length == timestamps.length, "Mismatched arrays");
        for (uint i = 0; i < bdioIds.length; i++) {
            require(msg.sender == bdioRegistry.getDocumentOwner(bdioIds[i]), "Not document owner");
            require(timestamps[i] == 0 || timestamps[i] > block.timestamp, "Invalid expiry");
            expiry[bdioIds[i]] = timestamps[i];
            emit ExpirySet(bdioIds[i], timestamps[i]);
        }
    }

    /// @notice Clear expiry (set to 0 = no expiry) for a single document
    /// @param bdioId Document ID
    function clearExpiry(string calldata bdioId) external {
        require(msg.sender == bdioRegistry.getDocumentOwner(bdioId), "Not document owner");
        expiry[bdioId] = 0;
        emit ExpirySet(bdioId, 0);
    }

    /// @notice Clear expiry for multiple documents in batch
    /// @param bdioIds List of document IDs
    function batchClearExpiry(string[] calldata bdioIds) external {
        for (uint i = 0; i < bdioIds.length; i++) {
            require(msg.sender == bdioRegistry.getDocumentOwner(bdioIds[i]), "Not document owner");
            expiry[bdioIds[i]] = 0;
            emit ExpirySet(bdioIds[i], 0);
        }
    }

    /// @notice Get expiry timestamp of a document
    /// @param bdioId Document ID
    /// @return expiry timestamp (0 = no expiry)
    function getExpiry(string calldata bdioId) external view returns (uint256) {
        return expiry[bdioId];
    }

    /// @notice Check if document is expired
    /// @param bdioId Document ID
    /// @return true if expired
    function isExpired(string calldata bdioId) external view returns (bool) {
        uint256 ts = expiry[bdioId];
        return ts != 0 && ts < block.timestamp;
    }
}
