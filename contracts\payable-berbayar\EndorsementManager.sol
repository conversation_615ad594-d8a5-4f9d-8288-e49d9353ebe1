// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

interface IAccessControlManager {
    function isSignerApproved(string calldata bdioId, address signer) external view returns (bool);
}

interface IBDIORegistry {
    function getDocumentOwner(string calldata bdioId) external view returns (address);
}

contract EndorsementManager {
    address public accessControlManager;
    IBDIORegistry public bdioRegistry;
    address public contractOwner;

    uint256 public endorsementFee = 0.15 ether;

    struct Endorsement {
        address signer;
        bytes signature;
        uint timestamp;
        string note;
        bool revoked;
    }

    // Mapping: bdioId => list of endorsements
    mapping(string => Endorsement[]) public endorsements;

    // Events
    event EndorsedWithNote(string indexed bdioId, address indexed signer, string note);
    event EndorsementRevoked(string indexed bdioId, uint index, address revokedBy);
    event Withdraw(address indexed to, uint256 amount);
    event EndorsementFeeUpdated(uint256 oldFee, uint256 newFee);

    constructor(address _accessControlManager, address registryAddress) {
        accessControlManager = _accessControlManager;
        bdioRegistry = IBDIORegistry(registryAddress);
        contractOwner = msg.sender;
    }

    modifier onlyContractOwner() {
        require(msg.sender == contractOwner, "Not contract owner");
        _;
    }

    /// Update endorsement fee (only owner)
    function setEndorsementFee(uint256 newFee) external onlyContractOwner {
        emit EndorsementFeeUpdated(endorsementFee, newFee);
        endorsementFee = newFee;
    }

    /// Sign single endorsement with note
    function signWithNote(
        string calldata bdioId,
        bytes calldata signature,
        string calldata note
    ) external payable {
        require(msg.value >= endorsementFee, "Insufficient endorsement fee");
        require(
            IAccessControlManager(accessControlManager).isSignerApproved(bdioId, msg.sender),
            "Not approved signer"
        );

        Endorsement[] storage list = endorsements[bdioId];
        for (uint i = 0; i < list.length; i++) {
            require(list[i].signer != msg.sender, "Already endorsed");
        }

        require(_verifySignature(bdioId, signature, msg.sender), "Invalid signature");

        list.push(
            Endorsement({
                signer: msg.sender,
                signature: signature,
                timestamp: block.timestamp,
                note: note,
                revoked: false
            })
        );

        emit EndorsedWithNote(bdioId, msg.sender, note);
    }

    /// Batch endorse multiple signers (with fee per signer)
    function batchEndorse(
        string calldata bdioId,
        address[] calldata signers,
        bytes[] calldata signatures,
        string calldata note
    ) external payable {
        uint count = signers.length;
        require(count == signatures.length, "Mismatched arrays");
        require(msg.value >= endorsementFee * count, "Insufficient total endorsement fee");

        for (uint i = 0; i < count; i++) {
            require(
                IAccessControlManager(accessControlManager).isSignerApproved(bdioId, signers[i]),
                "Signer not approved"
            );

            Endorsement[] storage list = endorsements[bdioId];
            for (uint j = 0; j < list.length; j++) {
                require(list[j].signer != signers[i], "Already endorsed");
            }

            require(_verifySignature(bdioId, signatures[i], signers[i]), "Invalid signature");

            list.push(
                Endorsement({
                    signer: signers[i],
                    signature: signatures[i],
                    timestamp: block.timestamp,
                    note: note,
                    revoked: false
                })
            );

            emit EndorsedWithNote(bdioId, signers[i], note);
        }
    }

    /// Revoke endorsement by index (only document owner)
    function revokeEndorsement(string calldata bdioId, uint index) external {
        require(msg.sender == bdioRegistry.getDocumentOwner(bdioId), "Only owner can revoke");
        require(index < endorsements[bdioId].length, "Invalid index");
        require(!endorsements[bdioId][index].revoked, "Already revoked");

        endorsements[bdioId][index].revoked = true;
        emit EndorsementRevoked(bdioId, index, msg.sender);
    }

    /// Get count of endorsements for a document
    function getEndorsementsCount(string calldata bdioId) external view returns (uint) {
        return endorsements[bdioId].length;
    }

    /// Get endorsement details by index
    function getEndorsementByIndex(string calldata bdioId, uint index) external view returns (
        address signer,
        bytes memory signature,
        uint timestamp,
        string memory note,
        bool revoked
    ) {
        Endorsement storage e = endorsements[bdioId][index];
        return (e.signer, e.signature, e.timestamp, e.note, e.revoked);
    }

    /// Get all endorsements for a document
    function getAllEndorsements(string calldata bdioId) external view returns (Endorsement[] memory) {
        return endorsements[bdioId];
    }

    /// Internal: verify ECDSA signature matches signer
    function _verifySignature(
        string calldata bdioId,
        bytes memory signature,
        address expectedSigner
    ) internal pure returns (bool) {
        bytes32 messageHash = keccak256(abi.encodePacked(bdioId));
        bytes32 ethSignedMessageHash = keccak256(
            abi.encodePacked("\x19Ethereum Signed Message:\n32", messageHash)
        );
        (bytes32 r, bytes32 s, uint8 v) = _splitSignature(signature);
        address recovered = ecrecover(ethSignedMessageHash, v, r, s);
        return (recovered == expectedSigner);
    }

    /// Split signature to r, s, v
    function _splitSignature(bytes memory sig) internal pure returns (
        bytes32 r,
        bytes32 s,
        uint8 v
    ) {
        require(sig.length == 65, "Invalid signature length");
        assembly {
            r := mload(add(sig, 32))
            s := mload(add(sig, 64))
            v := byte(0, mload(add(sig, 96)))
        }
    }

    /// Withdraw all collected fees to owner
    function withdrawAll() external onlyContractOwner {
        uint256 balance = address(this).balance;
        require(balance > 0, "No balance to withdraw");
        payable(contractOwner).transfer(balance);
        emit Withdraw(contractOwner, balance);
    }

    /// Withdraw specific amount to target address
    function withdrawTo(address payable to, uint256 amount) external onlyContractOwner {
        require(to != address(0), "Invalid address");
        require(address(this).balance >= amount, "Insufficient contract balance");
        to.transfer(amount);
        emit Withdraw(to, amount);
    }
}
