// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/security/ReentrancyGuard.sol";
import "@openzeppelin/contracts/security/Pausable.sol";

interface IAccessControlManager {
    function isSignerApproved(string calldata bdioId, address signer) external view returns (bool);
}

interface IBDIORegistry {
    function getDocumentOwner(string calldata bdioId) external view returns (address);
}

contract EndorsementManager is ReentrancyGuard, Pausable {
    address public accessControlManager;
    IBDIORegistry public bdioRegistry;
    address public contractOwner;
    address public pendingOwner;

    uint256 public endorsementFee = 0.15 ether;
    uint256 public constant MAX_NOTE_LENGTH = 500;
    uint256 public constant MAX_BATCH_SIZE = 50;
    uint256 public constant MAX_PAGINATION_LIMIT = 100;

    struct Endorsement {
        address signer;
        bytes signature;
        uint timestamp;
        string note;
        bool revoked;
    }

    // Mapping: bdioId => list of endorsements
    mapping(string => Endorsement[]) public endorsements;
    
    // Mapping to track if a signer has already endorsed a document
    mapping(string => mapping(address => bool)) public hasEndorsed;

    // Events
    event EndorsedWithNote(string indexed bdioId, address indexed signer, string note);
    event EndorsementRevoked(string indexed bdioId, uint index, address revokedBy);
    event Withdraw(address indexed to, uint256 amount);
    event EndorsementFeeUpdated(uint256 oldFee, uint256 newFee);
    event AccessControlManagerUpdated(address indexed oldManager, address indexed newManager);
    event RegistryUpdated(address indexed oldRegistry, address indexed newRegistry);
    event OwnershipTransferInitiated(address indexed currentOwner, address indexed newOwner);
    event OwnershipTransferCompleted(address indexed oldOwner, address indexed newOwner);

    modifier onlyContractOwner() {
        require(msg.sender == contractOwner, "Not contract owner");
        _;
    }

    modifier onlyDocumentOwner(string calldata bdioId) {
        require(msg.sender == bdioRegistry.getDocumentOwner(bdioId), "Only owner can perform this action");
        _;
    }

    modifier validAddress(address addr) {
        require(addr != address(0), "Invalid address");
        _;
    }

    modifier validContract(address addr) {
        require(addr != address(0), "Invalid address");
        require(addr.code.length > 0, "Not a contract");
        _;
    }

    modifier validBdioId(string calldata bdioId) {
        require(bytes(bdioId).length >= 10 && bytes(bdioId).length <= 64, "Invalid BDIO ID");
        _;
    }

    modifier validNote(string calldata note) {
        require(bytes(note).length <= MAX_NOTE_LENGTH, "Note too long");
        _;
    }

    constructor(address _accessControlManager, address registryAddress) {
        require(_accessControlManager != address(0), "Invalid access control manager");
        require(registryAddress != address(0), "Invalid registry address");
        
        accessControlManager = _accessControlManager;
        bdioRegistry = IBDIORegistry(registryAddress);
        contractOwner = msg.sender;
    }

    /// Update endorsement fee (only owner)
    function setEndorsementFee(uint256 newFee) external onlyContractOwner {
        emit EndorsementFeeUpdated(endorsementFee, newFee);
        endorsementFee = newFee;
    }

    /// Update access control manager (only owner)
    function updateAccessControlManager(address newManager) external
        onlyContractOwner
        validContract(newManager)
    {
        address oldManager = accessControlManager;
        accessControlManager = newManager;
        emit AccessControlManagerUpdated(oldManager, newManager);
    }

    /// Update registry address
    function updateRegistry(address newRegistry) external
        onlyContractOwner
        validContract(newRegistry)
    {
        address oldRegistry = address(bdioRegistry);
        bdioRegistry = IBDIORegistry(newRegistry);
        emit RegistryUpdated(oldRegistry, newRegistry);
    }

    /// Sign single endorsement with note
    function signWithNote(
        string calldata bdioId,
        bytes calldata signature,
        string calldata note
    ) external payable 
        nonReentrant
        whenNotPaused
        validBdioId(bdioId)
        validNote(note)
    {
        require(msg.value >= endorsementFee, "Insufficient endorsement fee");
        require(
            IAccessControlManager(accessControlManager).isSignerApproved(bdioId, msg.sender),
            "Not approved signer"
        );
        require(!hasEndorsed[bdioId][msg.sender], "Already endorsed");
        require(_verifySignature(bdioId, signature, msg.sender), "Invalid signature");

        endorsements[bdioId].push(
            Endorsement({
                signer: msg.sender,
                signature: signature,
                timestamp: block.timestamp,
                note: note,
                revoked: false
            })
        );

        hasEndorsed[bdioId][msg.sender] = true;
        emit EndorsedWithNote(bdioId, msg.sender, note);
    }

    /// Batch endorse multiple signers (with fee per signer)
    function batchEndorse(
        string calldata bdioId,
        address[] calldata signers,
        bytes[] calldata signatures,
        string calldata note
    ) external payable 
        nonReentrant
        whenNotPaused
        validBdioId(bdioId)
        validNote(note)
    {
        uint count = signers.length;
        require(count > 0 && count <= MAX_BATCH_SIZE, "Invalid batch size");
        require(count == signatures.length, "Mismatched arrays");
        
        // Safe multiplication check
        uint256 totalFee = endorsementFee * count;
        require(totalFee / count == endorsementFee, "Overflow detected");
        require(msg.value >= totalFee, "Insufficient total endorsement fee");

        for (uint i = 0; i < count; i++) {
            require(signers[i] != address(0), "Invalid signer address");
            require(
                IAccessControlManager(accessControlManager).isSignerApproved(bdioId, signers[i]),
                "Signer not approved"
            );
            require(!hasEndorsed[bdioId][signers[i]], "Signer already endorsed");
            require(_verifySignature(bdioId, signatures[i], signers[i]), "Invalid signature");

            endorsements[bdioId].push(
                Endorsement({
                    signer: signers[i],
                    signature: signatures[i],
                    timestamp: block.timestamp,
                    note: note,
                    revoked: false
                })
            );

            hasEndorsed[bdioId][signers[i]] = true;
            emit EndorsedWithNote(bdioId, signers[i], note);
        }
    }

    /// Revoke endorsement by index (only document owner)
    function revokeEndorsement(string calldata bdioId, uint index) external 
        onlyDocumentOwner(bdioId)
        validBdioId(bdioId)
        whenNotPaused
    {
        require(index < endorsements[bdioId].length, "Invalid index");
        require(!endorsements[bdioId][index].revoked, "Already revoked");

        endorsements[bdioId][index].revoked = true;
        emit EndorsementRevoked(bdioId, index, msg.sender);
    }

    /// Get count of endorsements for a document
    function getEndorsementsCount(string calldata bdioId) external view returns (uint) {
        return endorsements[bdioId].length;
    }

    /// Get endorsement details by index
    function getEndorsementByIndex(string calldata bdioId, uint index) external view returns (
        address signer,
        bytes memory signature,
        uint timestamp,
        string memory note,
        bool revoked
    ) {
        require(index < endorsements[bdioId].length, "Invalid index");
        Endorsement storage e = endorsements[bdioId][index];
        return (e.signer, e.signature, e.timestamp, e.note, e.revoked);
    }

    /// Get endorsements with pagination to prevent DoS
    function getEndorsementsPaginated(string calldata bdioId, uint256 offset, uint256 limit) 
        external view returns (Endorsement[] memory endorsementList, uint256 total) 
    {
        require(limit > 0 && limit <= MAX_PAGINATION_LIMIT, "Invalid limit");
        
        Endorsement[] memory allEndorsements = endorsements[bdioId];
        total = allEndorsements.length;
        
        if (offset >= total) {
            return (new Endorsement[](0), total);
        }
        
        uint256 end = offset + limit;
        if (end > total) {
            end = total;
        }
        
        endorsementList = new Endorsement[](end - offset);
        for (uint256 i = offset; i < end; i++) {
            endorsementList[i - offset] = allEndorsements[i];
        }
    }

    /// Get all endorsements for a document (backward compatibility)
    function getAllEndorsements(string calldata bdioId) external view returns (Endorsement[] memory) {
        return endorsements[bdioId];
    }

    /// Check if a signer has endorsed a document
    function hasSignerEndorsed(string calldata bdioId, address signer) external view returns (bool) {
        return hasEndorsed[bdioId][signer];
    }

    /// Internal: verify ECDSA signature matches signer (with contract address to prevent replay)
    function _verifySignature(
        string calldata bdioId,
        bytes memory signature,
        address expectedSigner
    ) internal view returns (bool) {
        bytes32 messageHash = keccak256(abi.encodePacked(bdioId, address(this), block.chainid));
        bytes32 ethSignedMessageHash = keccak256(
            abi.encodePacked("\x19Ethereum Signed Message:\n32", messageHash)
        );
        (bytes32 r, bytes32 s, uint8 v) = _splitSignature(signature);
        address recovered = ecrecover(ethSignedMessageHash, v, r, s);
        return (recovered == expectedSigner);
    }

    /// Split signature to r, s, v
    function _splitSignature(bytes memory sig) internal pure returns (
        bytes32 r,
        bytes32 s,
        uint8 v
    ) {
        require(sig.length == 65, "Invalid signature length");
        assembly {
            r := mload(add(sig, 32))
            s := mload(add(sig, 64))
            v := byte(0, mload(add(sig, 96)))
        }
    }

    /// Emergency pause
    function pause() external onlyContractOwner {
        _pause();
    }

    function unpause() external onlyContractOwner {
        _unpause();
    }

    /// Secure withdraw functions
    function withdrawAll() external onlyContractOwner nonReentrant {
        uint256 balance = address(this).balance;
        require(balance > 0, "No balance to withdraw");
        
        emit Withdraw(contractOwner, balance);
        
        (bool success, ) = payable(contractOwner).call{value: balance}("");
        require(success, "Transfer failed");
    }

    /// Withdraw specific amount to target address
    function withdrawTo(address payable to, uint256 amount) external 
        onlyContractOwner 
        validAddress(to)
        nonReentrant 
    {
        require(address(this).balance >= amount, "Insufficient contract balance");
        
        emit Withdraw(to, amount);
        
        (bool success, ) = to.call{value: amount}("");
        require(success, "Transfer failed");
    }

    /// @notice Initiate ownership transfer (2-step process)
    function transferOwnership(address newOwner) external onlyContractOwner validAddress(newOwner) {
        require(newOwner != contractOwner, "Same owner");
        pendingOwner = newOwner;
        emit OwnershipTransferInitiated(contractOwner, newOwner);
    }

    /// @notice Accept ownership transfer
    function acceptOwnership() external {
        require(msg.sender == pendingOwner, "Not pending owner");
        address oldOwner = contractOwner;
        contractOwner = pendingOwner;
        pendingOwner = address(0);
        emit OwnershipTransferCompleted(oldOwner, contractOwner);
    }

    /// @notice Cancel pending ownership transfer
    function cancelOwnershipTransfer() external onlyContractOwner {
        require(pendingOwner != address(0), "No pending transfer");
        pendingOwner = address(0);
    }
}