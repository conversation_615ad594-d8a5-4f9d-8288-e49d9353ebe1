// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

interface IAccessControlManager {
    function isSignerApproved(string calldata bdioId, address signer) external view returns (bool);
}

interface IBDIORegistry {
    function getDocumentOwner(string calldata bdioId) external view returns (address);
}

contract EndorsementManager {
    address public accessControlManager;
    IBDIORegistry public bdioRegistry;

    struct Endorsement {
        address signer;
        bytes signature;
        uint timestamp;
        string note;
        bool revoked;
    }

    mapping(string => Endorsement[]) public endorsements;

    event EndorsedWithNote(string indexed bdioId, address indexed signer, string note);
    event EndorsementRevoked(string indexed bdioId, uint index, address revokedBy);

    constructor(address _accessControlManager, address registryAddress) {
        accessControlManager = _accessControlManager;
        bdioRegistry = IBDIORegistry(registryAddress);
    }

    function signWithNote(
        string calldata bdioId,
        bytes calldata signature,
        string calldata note
    ) external {
        require(
            IAccessControlManager(accessControlManager).isSignerApproved(bdioId, msg.sender),
            "Not approved signer"
        );

        // Pastikan signer belum pernah endorse dokumen ini
        Endorsement[] storage list = endorsements[bdioId];
        for (uint i = 0; i < list.length; i++) {
            require(list[i].signer != msg.sender, "Already endorsed");
        }

        // Verifikasi signature on-chain
        require(_verifySignature(bdioId, signature, msg.sender), "Invalid signature");

        list.push(
            Endorsement({
                signer: msg.sender,
                signature: signature,
                timestamp: block.timestamp,
                note: note,
                revoked: false
            })
        );

        emit EndorsedWithNote(bdioId, msg.sender, note);
    }

    function batchEndorse(
        string calldata bdioId,
        address[] calldata signers,
        bytes[] calldata signatures,
        string calldata note
    ) external {
        require(signers.length == signatures.length, "Mismatched arrays");

        for (uint i = 0; i < signers.length; i++) {
            require(
                IAccessControlManager(accessControlManager).isSignerApproved(bdioId, signers[i]),
                "Signer not approved"
            );

            // Pastikan signer belum pernah endorse dokumen ini
            Endorsement[] storage list = endorsements[bdioId];
            for (uint j = 0; j < list.length; j++) {
                require(list[j].signer != signers[i], "Already endorsed");
            }

            // Verifikasi signature
            require(_verifySignature(bdioId, signatures[i], signers[i]), "Invalid signature");

            endorsements[bdioId].push(
                Endorsement({
                    signer: signers[i],
                    signature: signatures[i],
                    timestamp: block.timestamp,
                    note: note,
                    revoked: false
                })
            );

            emit EndorsedWithNote(bdioId, signers[i], note);
        }
    }

    function revokeEndorsement(string calldata bdioId, uint index) external {
        require(msg.sender == bdioRegistry.getDocumentOwner(bdioId), "Only owner can revoke");
        require(index < endorsements[bdioId].length, "Invalid index");
        require(!endorsements[bdioId][index].revoked, "Already revoked");

        endorsements[bdioId][index].revoked = true;
        emit EndorsementRevoked(bdioId, index, msg.sender);
    }

    function getEndorsementsCount(string calldata bdioId) external view returns (uint) {
        return endorsements[bdioId].length;
    }

    function getEndorsementByIndex(string calldata bdioId, uint index) external view returns (
        address signer,
        bytes memory signature,
        uint timestamp,
        string memory note,
        bool revoked
    ) {
        Endorsement storage e = endorsements[bdioId][index];
        return (e.signer, e.signature, e.timestamp, e.note, e.revoked);
    }

    function getAllEndorsements(string calldata bdioId) external view returns (Endorsement[] memory) {
        return endorsements[bdioId];
    }

    function _verifySignature(
        string calldata bdioId,
        bytes memory signature,
        address expectedSigner
    ) internal pure returns (bool) {
        bytes32 messageHash = keccak256(abi.encodePacked(bdioId));

        // Ethereum Signed Message hash
        bytes32 ethSignedMessageHash = keccak256(
            abi.encodePacked("\x19Ethereum Signed Message:\n32", messageHash)
        );

        (bytes32 r, bytes32 s, uint8 v) = _splitSignature(signature);
        address recovered = ecrecover(ethSignedMessageHash, v, r, s);
        return (recovered == expectedSigner);
    }

    function _splitSignature(bytes memory sig) internal pure returns (
        bytes32 r,
        bytes32 s,
        uint8 v
    ) {
        require(sig.length == 65, "Invalid signature length");
        assembly {
            r := mload(add(sig, 32))
            s := mload(add(sig, 64))
            v := byte(0, mload(add(sig, 96)))
        }
    }
}
