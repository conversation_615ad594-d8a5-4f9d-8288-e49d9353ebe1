// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/token/ERC721/extensions/ERC721Enumerable.sol";
import "@openzeppelin/contracts/utils/Strings.sol";

contract BDIOCoreRegistry is ERC721Enumerable {
    using Strings for uint256;

    address public contractOwner;
    uint256 public nextTokenId;
    string public baseURI;

    struct Version {
        string hashHex;
        uint256 timestamp;
        string note;
        address editor;
        string txHash;
    }

    struct OwnershipHistory {
        address owner;
        uint256 timestamp;
        string note;
    }

    struct Document {
        string hashHex;
        string title;
        string category;
        string metadataUri;
        address owner;
        bool active;
        bool archived;
        uint256 timestamp;
        Version[] versions;
        OwnershipHistory[] history;
    }

    mapping(string => Document) public registry;
    mapping(string => uint256) public bdioToTokenId;
    mapping(uint256 => string) public tokenIdToBdio;
    mapping(string => bool) public exists;

    modifier onlyContractOwner() {
        require(msg.sender == contractOwner, "Not contract owner");
        _;
    }

    modifier onlyDocOwner(string calldata bdioId) {
        require(registry[bdioId].owner == msg.sender, "Not document owner");
        _;
    }

    event DocumentRegistered(string indexed bdioId, address indexed owner);
    event DocumentBatchRegistered(uint count);
    event OwnershipTransferred(string indexed bdioId, address indexed oldOwner, address indexed newOwner);
    event VersionAdded(string indexed bdioId, string newHash, string note, address indexed editor);
    event DocumentArchived(string indexed bdioId, string reason, uint256 timestamp);
    event DocumentDeactivated(string indexed bdioId, string reason, uint256 timestamp);

    constructor() ERC721("BDIONFT", "BDIO") {
        contractOwner = msg.sender;
        baseURI = "http://localhost:3000/metadata/";
    }

    function setBaseURI(string calldata newBaseURI) external onlyContractOwner {
        baseURI = newBaseURI;
    }

    function register(
        string calldata bdioId,
        string calldata hashHex,
        string calldata title,
        string calldata category,
        string calldata metadataUri
    ) external {
        require(!exists[bdioId], "Already exists");

        Document storage doc = registry[bdioId];
        doc.hashHex = hashHex;
        doc.title = title;
        doc.category = category;
        doc.metadataUri = metadataUri;
        doc.owner = msg.sender;
        doc.active = true;
        doc.archived = false;
        doc.timestamp = block.timestamp;

        doc.versions.push(Version({
            hashHex: hashHex,
            timestamp: block.timestamp,
            note: "Initial",
            editor: msg.sender,
            txHash: ""
        }));

        doc.history.push(OwnershipHistory({
            owner: msg.sender,
            timestamp: block.timestamp,
            note: "Initial owner"
        }));

        exists[bdioId] = true;

        emit DocumentRegistered(bdioId, msg.sender);
    }

    function mintNFT(string calldata bdioId) external onlyDocOwner(bdioId) {
        require(bdioToTokenId[bdioId] == 0, "Already minted");

        uint256 newTokenId = nextTokenId + 1;
        require(bytes(tokenIdToBdio[newTokenId]).length == 0, "tokenId already used");

        // ✅ FIXED: assign mapping sebelum mint
        bdioToTokenId[bdioId] = newTokenId;
        tokenIdToBdio[newTokenId] = bdioId;

        nextTokenId = newTokenId;
        _safeMint(msg.sender, newTokenId);
    }

    function burnNFT(string calldata bdioId) external onlyDocOwner(bdioId) {
        uint256 tokenId = bdioToTokenId[bdioId];
        require(tokenId != 0, "Not minted");
        _burn(tokenId);
        bdioToTokenId[bdioId] = 0;
        delete tokenIdToBdio[tokenId];
    }

    function addVersion(
        string calldata bdioId,
        string calldata newHash,
        string calldata note,
        string calldata txHash
    ) external onlyDocOwner(bdioId) {
        require(exists[bdioId], "Not found");

        registry[bdioId].versions.push(Version({
            hashHex: newHash,
            timestamp: block.timestamp,
            note: note,
            editor: msg.sender,
            txHash: txHash
        }));

        emit VersionAdded(bdioId, newHash, note, msg.sender);
    }

    function archiveDocument(string calldata bdioId, string calldata reason) external onlyDocOwner(bdioId) {
        require(!registry[bdioId].archived, "Already archived");
        registry[bdioId].archived = true;
        emit DocumentArchived(bdioId, reason, block.timestamp);
    }

    function deactivateDocument(string calldata bdioId, string calldata reason) external onlyDocOwner(bdioId) {
        require(registry[bdioId].active, "Already inactive");
        registry[bdioId].active = false;
        emit DocumentDeactivated(bdioId, reason, block.timestamp);
    }

    function transferOwnershipManual(string calldata bdioId, address newOwner, string calldata note) external onlyDocOwner(bdioId) {
        require(newOwner != address(0), "Invalid new owner");

        address oldOwner = registry[bdioId].owner;
        registry[bdioId].owner = newOwner;

        registry[bdioId].history.push(OwnershipHistory({
            owner: newOwner,
            timestamp: block.timestamp,
            note: note
        }));

        emit OwnershipTransferred(bdioId, oldOwner, newOwner);
    }

    function _afterTokenTransfer(address from, address to, uint256 tokenId, uint256 /*batchSize*/) internal override {
        string memory bdioId = tokenIdToBdio[tokenId];
        require(bytes(bdioId).length != 0, "Unknown tokenId");

        address oldOwner = registry[bdioId].owner;
        registry[bdioId].owner = to;

        registry[bdioId].history.push(OwnershipHistory({
            owner: to,
            timestamp: block.timestamp,
            note: from == address(0) ? "NFT mint" : "NFT transfer"
        }));

        emit OwnershipTransferred(bdioId, oldOwner, to);
    }

    function tokenURI(uint256 tokenId) public view override returns (string memory) {
        return string(abi.encodePacked(baseURI, tokenId.toString(), ".json"));
    }

    function getDocumentOwner(string calldata bdioId) external view returns (address) {
        return registry[bdioId].owner;
    }

    function getDocumentVersions(string calldata bdioId) external view returns (Version[] memory) {
        return registry[bdioId].versions;
    }

    function getOwnershipHistory(string calldata bdioId) external view returns (OwnershipHistory[] memory) {
        return registry[bdioId].history;
    }

    function verifyDocument(string calldata bdioId) external view returns (
        address owner,
        bool active,
        bool archived,
        uint256 timestamp,
        uint versionCount,
        string memory category,
        string memory metadataUri
    ) {
        Document storage doc = registry[bdioId];
        return (
            doc.owner,
            doc.active,
            doc.archived,
            doc.timestamp,
            doc.versions.length,
            doc.category,
            doc.metadataUri
        );
    }

    function getDocumentByTokenId(uint256 tokenId) external view returns (string memory bdioId, address owner) {
        bdioId = tokenIdToBdio[tokenId];
        require(bytes(bdioId).length != 0, "Not linked");
        owner = registry[bdioId].owner;
    }

    function supportsInterface(bytes4 interfaceId) public view override returns (bool) {
        return super.supportsInterface(interfaceId);
    }
}
