<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Register Document - creden.xyz</title>
    <script src="https://cdn.jsdelivr.net/npm/ethers@6.7.0/dist/ethers.umd.min.js"></script>
    <style>
      body {
        font-family: sans-serif;
        max-width: 600px;
        margin: auto;
        padding: 20px;
      }
      label,
      input,
      select,
      button {
        display: block;
        width: 100%;
        margin: 10px 0;
      }
      #status {
        white-space: pre-wrap;
        background: #f8f8f8;
        padding: 10px;
        border: 1px solid #ddd;
        border-radius: 5px;
      }
    </style>
  </head>
  <body>
    <h2>Register Document</h2>

    <label>Mode *</label>
    <select id="mode">
      <option value="single">Single Document</option>
      <option value="multiple">Multiple Documents</option>
    </select>

    <label>Title *</label>
    <input type="text" id="title" placeholder="e.g. Training Certificate" />

    <label>Category (optional)</label>
    <input type="text" id="category" placeholder="e.g. Legal, HR" />

    <input hidden type="text" id="metadataUri" placeholder="e.g. https://..." />

    <label>Select File *</label>
    <input type="file" id="file" />

    <button id="btnRegister" onclick="startRegister()">Register Document</button>
    <div id="status">Ready</div>

    <script>
      const registerFee = "0.2"; // Example register fee in POL/ETH
      let contract; // Assume you initialize contract elsewhere

      async function startRegister() {
        const mode = document.getElementById("mode").value;
        if (mode === "multiple") await batchRegister();
        else await register();
      }

      function log(msg) {
        const s = document.getElementById("status");
        s.textContent = msg;
      }

      document.getElementById("mode").addEventListener("change", function () {
        const mode = this.value;
        const fileInput = document.getElementById("file");
        if (mode === "multiple") {
          fileInput.setAttribute("multiple", "");
        } else {
          fileInput.removeAttribute("multiple");
          fileInput.value = ""; // opsional: clear file input
        }
      });

      async function batchRegister() {
        try {
          const title = document.getElementById("title").value.trim();
          const category = document.getElementById("category").value.trim();
          const metadataUri = document.getElementById("metadataUri").value.trim();
          const fileInput = document.getElementById("file");
          const btn = document.getElementById("btnRegister");
          if (!title || fileInput.files.length === 0) return log("⚠️ Title & files required.");
          btn.disabled = true;

          const files = Array.from(fileInput.files);
          const bdioIds = [],
            hashHexes = [],
            titles = [],
            categories = [],
            metadataUris = [],
            expiries = [];
          log("🔍 Hashing files...");

          for (const file of files) {
            const arrayBuffer = await file.arrayBuffer();
            const hashHex = ethers.keccak256(new Uint8Array(arrayBuffer));
            const bdioId = hashHex.slice(-10);
            bdioIds.push(bdioId);
            hashHexes.push(hashHex);
            titles.push(title);
            categories.push(category);
            metadataUris.push(metadataUri || "");
            expiries.push(0);
          }

          log(`📡 Sending batch register for ${bdioIds.length} documents...`);
          const tx = await contract.batchRegister(bdioIds, hashHexes, titles, categories, metadataUris, expiries, { value: ethers.parseEther((Number(registerFee) * bdioIds.length).toString()) });

          log("⏳ Waiting for confirmation...");
          const receipt = await tx.wait();
          log("✅ Batch registered! TxHash: " + receipt.hash + "\n🆔 BDIO IDs:\n" + bdioIds.join("\n"));
        } catch (e) {
          console.error(e);
          log("❌ Error: " + (e?.message || e));
        } finally {
          document.getElementById("btnRegister").disabled = false;
        }
      }

      async function register() {
        try {
          // existing single register logic...
          log("✅ Single register logic here...");
        } catch (e) {
          console.error(e);
          log("❌ Error: " + (e?.message || e));
        }
      }
    </script>
  </body>
</html>