<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8" />
<meta name="viewport" content="width=device-width, initial-scale=1" />
<title>🪙 NFT Wrapper</title>
<script src="https://cdn.jsdelivr.net/npm/ethers@6.7.0/dist/ethers.umd.min.js"></script>
<style>
  body { font-family: 'Segoe UI', Tahoma, sans-serif; background: #f9fafb; color: #333;
    max-width: 500px; margin: 2em auto; padding: 2em; border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1); background-color: #fff; }
  h2 { text-align: center; color: #1e40af; margin-bottom: 1em; }
  label { margin-top: 1em; display: block; font-weight: 500; }
  input[type="text"] { width: 100%; padding: 0.6em; margin-top: 0.3em;
    border: 1px solid #ccc; border-radius: 4px; }
  button { width: 100%; padding: 0.8em; margin-top: 1em; background: #1e40af; color: #fff;
    border: none; border-radius: 4px; font-weight: 600; cursor: pointer; transition: background 0.2s; }
  button:hover { background: #1a3691; }
  #status { margin-top: 1em; padding: 0.8em; background: #f1f5f9; border-left: 4px solid #1e40af;
    font-size: 0.95em; white-space: pre-wrap; border-radius: 4px; min-height: 3em; }
</style>
</head>
<body>

<h2>🪙 NFT Wrapper</h2>

<div>
  <strong>Connected Wallet:</strong> <span id="walletAddress">Not connected</span><br/>
  <strong>Contract Owner:</strong> <span id="ownerAddress">Loading...</span><br/>
  <button onclick="loadOwner()">🔄 Refresh Owner</button>
</div>

<hr/>
<h3>Transfer Ownership</h3>
<label>BDIO ID</label>
<input type="text" id="transferBdioId" placeholder="Enter BDIO ID" />
<label>New Owner Address</label>
<input type="text" id="newOwner" placeholder="0x..." />
<label>Note</label>
<input type="text" id="transferNote" placeholder="Enter note for transfer" />
<button onclick="transferOwnership()">Transfer Ownership</button>

<hr/>
<h3>Mint NFT</h3>
<label>BDIO ID</label>
<input type="text" id="mintBdioId" placeholder="Enter BDIO ID" />
<button onclick="mintNFT()">Mint NFT</button>

<hr/>
<h3>Burn NFT</h3>
<label>BDIO ID</label>
<input type="text" id="burnBdioId" placeholder="Enter BDIO ID" />
<button onclick="burnNFT()">Burn NFT</button>

<hr/>
<h3>Query Token ID & URI</h3>
<label>BDIO ID</label>
<input type="text" id="queryBdioId" placeholder="Enter BDIO ID" />
<button onclick="queryTokenIdAndURI()">Get Token ID & URI</button>
<div id="queryResult"></div>

<div id="status">Ready</div>

<script>
const CONTRACT_ADDRESS = '******************************************';
let provider, signer, contract, CONTRACT_ABI;

async function loadABI() {
  try {
    const res = await fetch('/abis/BDIOCoreRegistry.json');
    CONTRACT_ABI = (await res.json()).abi;
  } catch (e) { log('❌ Failed to load ABI: ' + e); }
}

async function init() {
  await loadABI();
  if (!window.ethereum) return log('❌ MetaMask not found.');
  provider = new ethers.BrowserProvider(window.ethereum);
  await provider.send("eth_requestAccounts", []);
  signer = await provider.getSigner();
  contract = new ethers.Contract(CONTRACT_ADDRESS, CONTRACT_ABI, signer);
  const addr = await signer.getAddress();
  document.getElementById('walletAddress').textContent = addr;
  log('✅ Wallet connected: ' + addr);
  await loadOwner();
}

async function loadOwner() {
  try {
    const owner = await contract.contractOwner();
    document.getElementById('ownerAddress').textContent = owner;
  } catch (e) { log('❌ Failed to load owner: ' + e); }
}

async function transferOwnership() {
  try {
    const bdioId = get('transferBdioId');
    const newOwner = get('newOwner');
    const note = get('transferNote');
    if (!ethers.isAddress(newOwner)) return log('⚠️ Invalid new owner address.');
    log('📡 Sending transferOwnershipManual...');
    const tx = await contract.transferOwnershipManual(bdioId, newOwner, note);
    log('⏳ Waiting for confirmation...');
    await tx.wait();
    log(`✅ Ownership transferred to ${newOwner} for BDIO ID: ${bdioId}`);
    await updateMetadata(bdioId);
    await loadOwner();
  } catch (e) { log('❌ ' + (e?.message || e)); }
}

async function mintNFT() {
  try {
    const bdioId = get('mintBdioId');
    log('📡 Sending mintNFT...');
    const tx = await contract.mintNFT(bdioId);
    await tx.wait();
    log(`✅ NFT minted for BDIO ID: ${bdioId}`);

    // Ambil tokenId dari contract
    const tokenIdRaw = await contract.bdioToTokenId(bdioId);
    const tokenId = tokenIdRaw.toString();

    // Panggil backend untuk buat file {tokenId}.json, sertakan tokenId & contract
    const res = await fetch('/api/saveNft', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        bdioId,
        tokenId,
        contract: CONTRACT_ADDRESS,
        name: bdioId,
        description: 'BDIO NFT',
        image: 'http://localhost:3000/assets/nft.png',
        external_url: `http://localhost:3000/get?bdioId=${bdioId}`,
        attributes: [
          { trait_type: 'Created At', value: new Date().toISOString() }
        ]
      })
    });
    if (res.ok) {
      log('✅ NFT metadata JSON saved on server.');
    } else {
      const err = await res.text();
      log('⚠️ Failed to save NFT metadata: ' + err);
    }

    await updateMetadata(bdioId); // update BDIO .meta.json juga
  } catch (e) {
    log('❌ ' + (e?.message || e));
  }
}

async function burnNFT() {
  try {
    const bdioId = get('burnBdioId');
    log('🔥 Sending burnNFT...');
    const tx = await contract.burnNFT(bdioId);
    log('⏳ Waiting for confirmation...');
    await tx.wait();
    log(`✅ NFT burned for BDIO ID: ${bdioId}`);
    await updateMetadata(bdioId);
  } catch (e) { log('❌ ' + (e?.message || e)); }
}

async function queryTokenIdAndURI() {
  try {
    const bdioId = get('queryBdioId');
    log('🔍 Querying token ID...');
    const tokenIdRaw = await contract.bdioToTokenId(bdioId);
    const tokenId = tokenIdRaw.toString();
    if (tokenId !== "0") {
      const uri = await contract.tokenURI(tokenId);
      document.getElementById('queryResult').innerHTML =
        `Token ID: ${tokenId}<br/>Token URI: <a href="${uri}" target="_blank">${uri}</a>`;
      log('✅ Token info loaded.');
    } else {
      document.getElementById('queryResult').textContent = 'No token minted for this BDIO ID.';
      log('ℹ️ No token found.');
    }
  } catch (e) { log('❌ ' + (e?.message || e)); }
}

async function updateMetadata(bdioId) {
  try {
    const res = await fetch('/api/updateMetadata', {
      method:'POST', headers:{'Content-Type':'application/json'},
      body: JSON.stringify({ bdioId })
    });
    res.ok ? log('✅ BDIO metadata updated on server.') : log('⚠️ Failed to update BDIO metadata.');
  } catch (e) { log('❌ Updating metadata failed: ' + e.message); }
}

function get(id) {
  const val = document.getElementById(id).value.trim();
  if (!val) throw '⚠️ Please fill all fields.';
  return val;
}
function log(msg) { document.getElementById('status').innerText = msg; }

window.onload = init;
</script>

</body>
</html>
