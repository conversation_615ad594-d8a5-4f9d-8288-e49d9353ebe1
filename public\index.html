<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8" />
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>📄 BDIO Document Dashboard</title>
<script src="https://cdn.jsdelivr.net/npm/ethers@6.7.0/dist/ethers.umd.min.js"></script>
<style>
  body { font-family: 'Segoe UI', Tahoma, sans-serif; background: #f9fafb; color: #333; max-width: 1000px; margin: 2em auto; padding: 2em; border-radius: 8px; background-color: #fff; box-shadow: 0 4px 12px rgba(0,0,0,0.1);}
  h2 { color: #1e40af; margin-top: 2em; }
  button { padding: 0.6em 1.2em; background: #1e40af; color: #fff; border: none; border-radius: 4px; font-weight: 600; cursor: pointer; margin-bottom: 1em; }
  button:hover { background: #1a3691; }
  table { width: 100%; border-collapse: collapse; margin-top: 1em; }
  th, td { border: 1px solid #ddd; padding: 0.7em; text-align: left; }
  th { background: #1e40af; color: white; }
  tr:nth-child(even) { background: #f1f5f9; }
  #status { margin-top: 1em; padding: 0.8em; background: #f1f5f9; border-left: 4px solid #1e40af; border-radius: 4px; }
</style>
</head>
<body>

<h1>📄 BDIO Document Dashboard</h1>
<button id="connectBtn" onclick="connect()">Connect Wallet</button>
<div id="userAddress"></div>
<div id="status">Please connect your wallet.</div>

<h2>📄 Your Documents</h2>
<table id="ownedTable" style="display:none;">
<thead>
<tr><th>BDIO ID</th><th>Title</th><th>Category</th><th>Status</th><th>Action</th></tr>
</thead>
<tbody id="ownedBody"></tbody>
</table>

<h2>✏️ Documents You're Approved To Sign</h2>
<table id="approvedTable" style="display:none;">
<thead>
<tr><th>BDIO ID</th><th>Title</th><th>Category</th><th>Signed?</th><th>Action</th></tr>
</thead>
<tbody id="approvedBody"></tbody>
</table>

<script>
const bdioCoreAddress = '******************************************';
const accessControlAddress = '******************************************';
const endorsementAddress = '******************************************';

const minimalAccessAbi = [
 "function isSignerApproved(string bdioId, address signer) view returns (bool)",
 "function getApprovedSigners(string bdioId) view returns (address[])"
];
const minimalEndorseAbi = [
 "function getEndorsementsCount(string bdioId) view returns (uint256)",
 "function getEndorsementByIndex(string bdioId, uint256 index) view returns (address signer, bytes signature, uint256 timestamp, string note)"
];

let provider, signer, userAddr, bdio, access, endorse, contractAbi;

async function loadABI() {
  const res = await fetch('abis/BDIOCoreRegistry.json');
  const json = await res.json();
  contractAbi = json.abi;
}

async function connect() {
 try {
   await provider.send("eth_requestAccounts", []);
   signer = await provider.getSigner();
   userAddr = await signer.getAddress();
   document.getElementById('userAddress').innerText = 'Connected as: ' + userAddr;
   document.getElementById('connectBtn').style.display = 'none';
   await loadDocuments();
 } catch(e){ log('❌ ' + e.message); }
}

async function init() {
 await loadABI();
 if(window.ethereum){
   provider = new ethers.BrowserProvider(window.ethereum);
   bdio = new ethers.Contract(bdioCoreAddress, contractAbi, provider);
   access = new ethers.Contract(accessControlAddress, minimalAccessAbi, provider);
   endorse = new ethers.Contract(endorsementAddress, minimalEndorseAbi, provider);
 } else {
   log('❌ MetaMask not found.');
 }
}

async function loadDocuments(){
 try{
   const count = await bdio.totalSupply();
   const owned=[], approved=[];
   for(let i=0;i<count;i++){
     const tokenId = await bdio.tokenByIndex(i);
     const bdioId = await bdio.tokenIdToBdio(tokenId);
     const doc = await bdio.verifyDocument(bdioId);
     const [owner,, archived,, createdAt,, metaUri] = doc;
     let title=''; try{
       const res=await fetch(metaUri); if(res.ok){ const j=await res.json(); title=j.title||''; }
     }catch{}
     const category=doc[5];

     // Check if user is owner or was owner
     const history = await bdio.getOwnershipHistory(bdioId);
     const isOwner = owner.toLowerCase()===userAddr.toLowerCase();
     const wasOwner = history.some(h=>h.owner.toLowerCase()===userAddr.toLowerCase());
     const approve = await access.isSignerApproved(bdioId, userAddr);
     const endorseCount= await endorse.getEndorsementsCount(bdioId);
     let signed=false;
     for(let j=0;j<endorseCount;j++){
      const e=await endorse.getEndorsementByIndex(bdioId,j);
      if(e.signer.toLowerCase()===userAddr.toLowerCase()){ signed=true; break; }
     }

     // Owned table
     if(isOwner||wasOwner){
       owned.push({bdioId,title,category,status: isOwner?'Initial Owner':'Verifiable copy'});
     }

     // Approved table
     if(approve){
       approved.push({bdioId,title,category,signed});
     }
   }
   renderTable('ownedTable','ownedBody',owned,false);
   renderTable('approvedTable','approvedBody',approved,true);
   log('✅ Documents loaded.');
 }catch(e){log('❌ '+e.message);}
}

function renderTable(tableId,bodyId,items,isApproved){
 const t=document.getElementById(tableId);
 const b=document.getElementById(bodyId);
 b.innerHTML='';
 for(const d of items){
  const tr=document.createElement('tr');
  if(isApproved){
   tr.innerHTML=`<td>${d.bdioId}</td><td>${d.title}</td><td>${d.category}</td>
   <td>${d.signed?'✅':'❌'}</td>
   <td>${d.signed?'<button disabled>Signed</button>':
   `<a href="endorse.html?bdioId=${encodeURIComponent(d.bdioId)}"><button>Need Sign</button></a>`}</td>`;
  }else{
   tr.innerHTML=`<td>${d.bdioId}</td><td>${d.title}</td><td>${d.category}</td>
   <td>${d.status}</td>
   <td><a href="get.html?bdioId=${encodeURIComponent(d.bdioId)}">View</a></td>`;
  }
  b.appendChild(tr);
 }
 t.style.display=items.length?'table':'none';
}

function log(msg){ document.getElementById('status').innerText=msg; }

window.onload=init;
</script>
</body>
</html>
