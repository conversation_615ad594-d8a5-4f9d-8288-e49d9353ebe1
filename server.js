// SPDX-License-Identifier: MIT
const express = require('express');
const path = require('path');
const fs = require('fs');
const { ethers } = require('ethers');
const dotenv = require('dotenv');
dotenv.config();

const app = express();
const PORT = 3000;

// ✅ Provider & contracts
const provider = new ethers.JsonRpcProvider(process.env.RPC_URL);
const BDIOCoreABI = require('./abis/BDIOCoreRegistry.json');
const AccessControlABI = require('./abis/AccessControlManager.json');
const ExpiryABI = require('./abis/BDIOExpiryManager.json');
const EndorsementABI = require('./abis/EndorsementManager.json');
const VCABI = require('./abis/VCManager.json');

const bdio = new ethers.Contract(process.env.BDIO_CORE_ADDRESS, BDIOCoreABI.abi, provider);
const access = new ethers.Contract(process.env.ACCESS_CONTROL_ADDRESS, AccessControlABI.abi, provider);
const expiry = new ethers.Contract(process.env.EXPIRY_MANAGER_ADDRESS, ExpiryABI.abi, provider);
const endorse = new ethers.Contract(process.env.ENDORSEMENT_MANAGER_ADDRESS, EndorsementABI.abi, provider);
const vc = new ethers.Contract(process.env.VC_MANAGER_ADDRESS, VCABI.abi, provider);

// Middleware
app.use(express.json());
app.use(express.static(path.join(__dirname, 'public')));
app.use('/metadata', express.static(path.join(__dirname, 'public', 'metadata')));
app.use('/abis', express.static(path.join(__dirname, 'abis')));

// ========== Serve HTML Pages ==========
app.get("/:page", (req, res) => {
  const filePath = path.join(process.cwd(), "public", `${req.params.page}.html`);
  fs.existsSync(filePath) ? res.sendFile(filePath) : res.status(404).send("Page not found");
});

const metadataDir = path.join(process.cwd(), 'public', 'metadata');
if (!fs.existsSync(metadataDir)) fs.mkdirSync(metadataDir, { recursive: true });

// ========= Helper: retry with delay =========
const retry = async (fn, retries = 3, delay = 1500) => {
  for (let i = 0; i < retries; i++) {
    try { return await fn(); }
    catch (e) { if (i === retries - 1) throw e; await new Promise(r => setTimeout(r, delay)); }
  }
};

// ========== saveMetadata ==========
app.post('/api/saveMetadata', async (req, res) => {
  try {
    const { bdioId } = req.body;
    if (!bdioId) return res.status(400).json({ error: 'Missing bdioId' });

    // Smart contract data
    const [owner, active, archived, ts, versionCount, categoryOnChain, metaUri] = await bdio.verifyDocument(bdioId);
    const versionsRaw = await retry(() => bdio.getDocumentVersions(bdioId));
    const ownership = await retry(() => bdio.getOwnershipHistory(bdioId));
    const expiryTime = await expiry.getExpiry(bdioId);
    const endorseCount = await endorse.getEndorsementsCount(bdioId);
    const tokenId = await bdio.bdioToTokenId(bdioId);
    const vcHash = await vc.vcHash(bdioId);

    // Endorsements
    const endorsements = [];
    for (let i = 0; i < endorseCount; i++) {
      const e = await endorse.getEndorsementByIndex(bdioId, i);
      endorsements.push({ signer: e[0], signature: e[1], timestamp: new Date(Number(e[2])*1000).toISOString(), note: e[3] });
    }

    const finalMetadata = {
      bdioId,
      title: req.body.title || '',
      category: req.body.category || ['default'],
      hashHex: req.body.hashHex || '',
      blockchaintxHash: req.body.blockchaintxHash || '',
      issuer: req.body.issuer || '',
      timestamp: new Date().toISOString(),
      expiry: expiryTime>0 ? new Date(Number(expiryTime)*1000).toISOString() : null,
      versions: versionsRaw.map(v => ({ hashHex: v.hashHex, timestamp: new Date(Number(v.timestamp)*1000).toISOString(), note: v.note })),
      signers: [], // optional
      ownershipHistory: ownership.map(o => ({ owner: o.owner, timestamp: new Date(Number(o.timestamp)*1000).toISOString(), note: o.note })),
      endorsements,
      nft: tokenId>0 ? { tokenId: Number(tokenId), contract: process.env.BDIO_CORE_ADDRESS } : null,
      vc: vcHash ? { hash: vcHash } : null,
      image: `http://localhost:3000/assets/doc.png`,
      metaUri: `https://localhost/metadata/${bdioId}.meta.json`
    };

    // Helper replacer to convert BigInt to string
    const jsonBigIntReplacer = (key, value) => typeof value === 'bigint' ? value.toString() : value;

    fs.writeFileSync(path.join(metadataDir, `${bdioId}.meta.json`), JSON.stringify(finalMetadata, jsonBigIntReplacer, 2), 'utf-8');
    console.log(`✅ Saved metadata for ${bdioId}`);
    res.json({ message: 'Metadata saved successfully' });
  } catch (e) {
    console.error('❌ Error:', e);
    res.status(500).json({ error: e.message || 'Failed to save metadata' });
  }
});

// ========== updateMetadata ==========
app.post('/api/updateMetadata', async (req, res) => {
  try {
    const { bdioId } = req.body;
    if (!bdioId) return res.status(400).json({ error: 'Missing bdioId' });

    const filePath = path.join(metadataDir, `${bdioId}.meta.json`);
    let existing = fs.existsSync(filePath) ? JSON.parse(fs.readFileSync(filePath,'utf-8')) : {};

    const [owner, active, archived, ts, versionCount, categoryOnChain, metaUri] = await bdio.verifyDocument(bdioId);
    if (!active || archived) return res.status(400).json({ error:'Document inactive or archived' });

    const versionsRaw = await bdio.getDocumentVersions(bdioId);
    const ownership = await bdio.getOwnershipHistory(bdioId);
    const signers = await access.getApprovedSigners(bdioId);
    const expiryTime = await expiry.getExpiry(bdioId);
    const endorseCount = await endorse.getEndorsementsCount(bdioId);
    const tokenId = await bdio.bdioToTokenId(bdioId);
    const vcHash = await vc.vcHash(bdioId);

    const endorsements = [];
    for (let i=0;i<endorseCount;i++) {
      const e=await endorse.getEndorsementByIndex(bdioId,i);
      endorsements.push({ signer:e[0], signature:e[1], timestamp:new Date(Number(e[2])*1000).toISOString(), note:e[3] });
    }

    const updated = {
      ...existing,
      expiry: expiryTime>0 ? new Date(Number(expiryTime)*1000).toISOString() : null,
      versions: versionsRaw.map(v=>({...v,timestamp:new Date(Number(v.timestamp)*1000).toISOString()})),
      signers,
      ownershipHistory: ownership.map(o=>({...o,timestamp:new Date(Number(o.timestamp)*1000).toISOString()})),
      endorsements,
      nft: tokenId>0 ? { tokenId:Number(tokenId), contract: process.env.BDIO_CORE_ADDRESS } : null,
      vc: vcHash ? { hash: vcHash } : null
    };

    // Helper replacer
    const jsonBigIntReplacer = (key, value) => typeof value === 'bigint' ? value.toString() : value;
    fs.writeFileSync(filePath, JSON.stringify(updated, jsonBigIntReplacer, 2), 'utf-8');
    console.log(`✅ Updated metadata for ${bdioId}`);

    // ✅ Auto generate {tokenId}.json if missing
    if (tokenId>0) {
      const nftFile = path.join(metadataDir, `${tokenId}.json`);
      if (!fs.existsSync(nftFile)) {
        console.log(`ℹ️ NFT metadata for tokenId ${tokenId} missing. Generating...`);
        const nftData = {
          bdioId,
          name: existing.title || bdioId,
          description: existing.description || 'BDIO NFT',
          image: existing.image || 'http://localhost:3000/assets/nft.png',
          external_url: `http://localhost:3000/get?bdioId=${bdioId}`,
          attributes: [
            { trait_type: 'Category', value: categoryOnChain },
            { trait_type: 'Created At', value: new Date(Number(ts)*1000).toISOString() }
          ]
        };
        fs.writeFileSync(nftFile, JSON.stringify(nftData, null, 2), 'utf-8');
        console.log(`✅ NFT metadata file created: ${tokenId}.json`);
      }
    }

    res.json({ message:'Metadata updated successfully' });
  } catch (e) {
    console.error('❌ Error updating metadata:', e);
    res.status(500).json({ error: e.message || 'Failed to update metadata' });
  }
});

// ========== saveNft ==========
app.post('/api/saveNft', async (req, res) => {
  try {
    const { bdioId, name, description, image, external_url, attributes } = req.body;
    if (!bdioId || !name) return res.status(400).json({ error:'Missing bdioId or name' });

    const tokenId = await retry(async () => {
      const t = (await bdio.bdioToTokenId(bdioId)).toString();
      if (t==='0') throw new Error('Token ID not yet available');
      return t;
    }, 3, 1000);

    const nftData = {
      bdioId,
      tokenId: Number(tokenId),
      contract: process.env.BDIO_CORE_ADDRESS,
      name,
      description: description || 'BDIO NFT',
      image: image || 'http://localhost:3000/assets/nft.png',
      external_url: external_url || '',
      attributes: Array.isArray(attributes) ? attributes : []
    };

    const filePath = path.join(metadataDir, `${tokenId}.json`);
    fs.writeFileSync(filePath, JSON.stringify(nftData, null, 2), 'utf-8');
    if (!fs.existsSync(filePath)) throw new Error('File write failed');

    console.log(`✅ Saved NFT metadata for tokenId ${tokenId}`);
    res.json({ message:'NFT metadata saved successfully' });
  } catch(e){
    console.error('❌ Error saving NFT metadata:', e);
    res.status(500).json({ error:e.message || 'Failed to save NFT metadata' });
  }
});

// ========== updateMetadataForAll ==========
const updateMetadataForAll = async () => {
  const files = fs.readdirSync(metadataDir).filter(f=>f.endsWith('.meta.json'));
  for (const file of files) {
    const bdioId=path.basename(file,'.meta.json');
    try {
      const resp = await fetch('http://localhost:3000/api/updateMetadata',{method:'POST',headers:{'Content-Type':'application/json'},body:JSON.stringify({bdioId})});
      console.log(`Updated ${bdioId}: ${await resp.text()}`);
    } catch(e) { console.error(`Error updating ${bdioId}:`,e); }
  }
};
setInterval(()=>updateMetadataForAll().catch(console.error),1*60*1000);

// Start server
app.listen(PORT,()=>console.log(`🚀 Server running at http://localhost:${PORT}`));
