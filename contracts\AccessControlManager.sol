// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

interface IBDIORegistry {
    function getDocumentOwner(string calldata bdioId) external view returns (address);
}

interface IEndorsementManager {
    function getEndorsementsCount(string calldata bdioId) external view returns (uint);
    function getEndorsementByIndex(string calldata bdioId, uint index) external view returns (
        address signer,
        bytes memory signature,
        uint timestamp,
        string memory note
    );
}

contract AccessControlManager {
    IBDIORegistry public bdioRegistry;
    address public endorsementManager;
    address public contractOwner;

    // Limit signer per document (default: 10)
    uint256 public maxSignersPerDocument = 10;

    // mapping: bdioId => signer => approved status
    mapping(string => mapping(address => bool)) public approvedSigners;

    // List all signer addresses ever approved (used to track revoked)
    mapping(string => address[]) private signerList;

    // Events
    event SignerApproved(string indexed bdioId, address indexed signer);
    event SignerRevoked(string indexed bdioId, address indexed signer);
    event EndorsementManagerUpdated(address indexed newAddress);
    event MaxSignersPerDocumentUpdated(uint256 oldMax, uint256 newMax);

    constructor(address registryAddress, address _endorsementManager) {
        bdioRegistry = IBDIORegistry(registryAddress);
        endorsementManager = _endorsementManager;
        contractOwner = msg.sender;
    }

    modifier onlyContractOwner() {
        require(msg.sender == contractOwner, "Not contract owner");
        _;
    }

    /// Approve a signer (only by document owner)
    function approveSigner(string calldata bdioId, address signer) external {
        require(msg.sender == bdioRegistry.getDocumentOwner(bdioId), "Only owner can approve signer");
        require(!approvedSigners[bdioId][signer], "Already approved");

        // Limit signer per document
        require(signerList[bdioId].length < maxSignersPerDocument, "Max signers reached");

        approvedSigners[bdioId][signer] = true;
        signerList[bdioId].push(signer);
        emit SignerApproved(bdioId, signer);
    }

    /// Revoke a previously approved signer
    function revokeSigner(string calldata bdioId, address signer) external {
        require(msg.sender == bdioRegistry.getDocumentOwner(bdioId), "Only owner can revoke signer");
        require(approvedSigners[bdioId][signer], "Signer not approved");

        approvedSigners[bdioId][signer] = false;
        emit SignerRevoked(bdioId, signer);
    }

    /// Check if signer is currently approved
    function isSignerApproved(string calldata bdioId, address signer) external view returns (bool) {
        return approvedSigners[bdioId][signer];
    }

    /// Get all signers ever approved (including revoked)
    function getApprovedSigners(string calldata bdioId) external view returns (address[] memory) {
        return signerList[bdioId];
    }

    /// Check if user is document owner (for frontend / admin checks)
    function isAdmin(string calldata bdioId, address user) external view returns (bool) {
        return user == bdioRegistry.getDocumentOwner(bdioId);
    }

    /// Get signer status: approved, signed (has endorsed), revoked
    function getSignerStatus(string calldata bdioId, address signer) external view returns (
        bool approved,
        bool signed,
        bool revoked
    ) {
        approved = approvedSigners[bdioId][signer];

        // Check if signer ever endorsed
        uint count = IEndorsementManager(endorsementManager).getEndorsementsCount(bdioId);
        signed = false;
        for (uint i = 0; i < count; i++) {
            (address s,,,) = IEndorsementManager(endorsementManager).getEndorsementByIndex(bdioId, i);
            if (s == signer) {
                signed = true;
                break;
            }
        }

        // revoked: was ever approved but currently not approved
        revoked = (!approved && _wasEverApproved(bdioId, signer));
    }

    /// Internal: check if signer ever approved (even if now revoked)
    function _wasEverApproved(string calldata bdioId, address signer) internal view returns (bool) {
        address[] memory list = signerList[bdioId];
        for (uint i = 0; i < list.length; i++) {
            if (list[i] == signer) return true;
        }
        return false;
    }

    /// Update endorsementManager address (only owner)
    function setEndorsementManager(address newAddr) external onlyContractOwner {
        require(newAddr != address(0), "Invalid address");
        endorsementManager = newAddr;
        emit EndorsementManagerUpdated(newAddr);
    }

    /// Update max signer limit (only owner)
    function setMaxSignersPerDocument(uint256 newMax) external onlyContractOwner {
        require(newMax > 0, "Invalid max");
        emit MaxSignersPerDocumentUpdated(maxSignersPerDocument, newMax);
        maxSignersPerDocument = newMax;
    }
}
