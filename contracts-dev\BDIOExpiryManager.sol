// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/security/ReentrancyGuard.sol";
import "@openzeppelin/contracts/security/Pausable.sol";

/// @title BDIO Expiry Manager
/// @notice Manage expiry date of documents registered in BDIORegistry
interface IBDIORegistry {
    function getDocumentOwner(string calldata bdioId) external view returns (address);
}

contract BDIOExpiryManager is ReentrancyGuard, Pausable {
    IBDIORegistry public bdioRegistry;
    address public contractOwner;
    address public pendingOwner;

    /// @dev Mapping: bdioId => expiry timestamp (0 = no expiry)
    mapping(string => uint256) public expiry;

    uint256 public constant MAX_BATCH_SIZE = 50;

    /// @notice Emitted when expiry is set or cleared
    event ExpirySet(string indexed bdioId, uint256 timestamp);
    event RegistryUpdated(address indexed oldRegistry, address indexed newRegistry);
    event OwnershipTransferInitiated(address indexed currentOwner, address indexed newOwner);
    event OwnershipTransferCompleted(address indexed oldOwner, address indexed newOwner);

    modifier onlyContractOwner() {
        require(msg.sender == contractOwner, "Not contract owner");
        _;
    }

    modifier onlyDocumentOwner(string calldata bdioId) {
        require(msg.sender == bdioRegistry.getDocumentOwner(bdioId), "Only owner can set expiry");
        _;
    }

    modifier validBdioId(string calldata bdioId) {
        require(bytes(bdioId).length >= 10 && bytes(bdioId).length <= 64, "Invalid BDIO ID");
        _;
    }

    modifier validAddress(address addr) {
        require(addr != address(0), "Invalid address");
        _;
    }

    modifier validContract(address addr) {
        require(addr != address(0), "Invalid address");
        require(addr.code.length > 0, "Not a contract");
        _;
    }

    constructor(address registryAddress) {
        require(registryAddress != address(0), "Invalid registry address");
        bdioRegistry = IBDIORegistry(registryAddress);
        contractOwner = msg.sender;
    }

    /// @notice Set expiry for a single document
    /// @param bdioId Document ID
    /// @param expiryTimestamp Unix timestamp (0 = no expiry)
    function setExpiry(string calldata bdioId, uint256 expiryTimestamp) external 
        onlyDocumentOwner(bdioId)
        validBdioId(bdioId)
        whenNotPaused
        nonReentrant
    {
        require(expiryTimestamp == 0 || expiryTimestamp > block.timestamp, "Invalid expiry");
        expiry[bdioId] = expiryTimestamp;
        emit ExpirySet(bdioId, expiryTimestamp);
    }

    /// @notice Set expiry for multiple documents in batch
    /// @param bdioIds List of document IDs
    /// @param timestamps List of expiry timestamps (0 = no expiry)
    function batchSetExpiry(string[] calldata bdioIds, uint256[] calldata timestamps) external
        whenNotPaused
        nonReentrant
    {
        require(bdioIds.length == timestamps.length, "Mismatched arrays");
        require(bdioIds.length > 0 && bdioIds.length <= MAX_BATCH_SIZE, "Invalid batch size");
        
        for (uint i = 0; i < bdioIds.length; i++) {
            require(bytes(bdioIds[i]).length >= 10 && bytes(bdioIds[i]).length <= 64, "Invalid BDIO ID");
            require(msg.sender == bdioRegistry.getDocumentOwner(bdioIds[i]), "Not document owner");
            require(timestamps[i] == 0 || timestamps[i] > block.timestamp, "Invalid expiry");
            
            expiry[bdioIds[i]] = timestamps[i];
            emit ExpirySet(bdioIds[i], timestamps[i]);
        }
    }

    /// @notice Clear expiry (set to 0 = no expiry) for a single document
    /// @param bdioId Document ID
    function clearExpiry(string calldata bdioId) external 
        onlyDocumentOwner(bdioId)
        validBdioId(bdioId)
        whenNotPaused
    {
        expiry[bdioId] = 0;
        emit ExpirySet(bdioId, 0);
    }

    /// @notice Clear expiry for multiple documents in batch
    /// @param bdioIds List of document IDs
    function batchClearExpiry(string[] calldata bdioIds) external
        whenNotPaused
        nonReentrant
    {
        require(bdioIds.length > 0 && bdioIds.length <= MAX_BATCH_SIZE, "Invalid batch size");
        
        for (uint i = 0; i < bdioIds.length; i++) {
            require(bytes(bdioIds[i]).length >= 10 && bytes(bdioIds[i]).length <= 64, "Invalid BDIO ID");
            require(msg.sender == bdioRegistry.getDocumentOwner(bdioIds[i]), "Not document owner");
            
            expiry[bdioIds[i]] = 0;
            emit ExpirySet(bdioIds[i], 0);
        }
    }

    /// @notice Get expiry timestamp of a document
    /// @param bdioId Document ID
    /// @return expiry timestamp (0 = no expiry)
    function getExpiry(string calldata bdioId) external view returns (uint256) {
        return expiry[bdioId];
    }

    /// @notice Check if document is expired
    /// @param bdioId Document ID
    /// @return true if expired
    function isExpired(string calldata bdioId) external view returns (bool) {
        uint256 ts = expiry[bdioId];
        return ts != 0 && ts < block.timestamp;
    }

    /// @notice Update registry address (only owner)
    function updateRegistry(address newRegistry) external
        onlyContractOwner
        validContract(newRegistry)
    {
        address oldRegistry = address(bdioRegistry);
        bdioRegistry = IBDIORegistry(newRegistry);
        emit RegistryUpdated(oldRegistry, newRegistry);
    }

    /// Emergency pause
    function pause() external onlyContractOwner {
        _pause();
    }

    function unpause() external onlyContractOwner {
        _unpause();
    }

    /// @notice Initiate ownership transfer (2-step process)
    function transferOwnership(address newOwner) external onlyContractOwner validAddress(newOwner) {
        require(newOwner != contractOwner, "Same owner");
        pendingOwner = newOwner;
        emit OwnershipTransferInitiated(contractOwner, newOwner);
    }

    /// @notice Accept ownership transfer
    function acceptOwnership() external {
        require(msg.sender == pendingOwner, "Not pending owner");
        address oldOwner = contractOwner;
        contractOwner = pendingOwner;
        pendingOwner = address(0);
        emit OwnershipTransferCompleted(oldOwner, contractOwner);
    }

    /// @notice Cancel pending ownership transfer
    function cancelOwnershipTransfer() external onlyContractOwner {
        require(pendingOwner != address(0), "No pending transfer");
        pendingOwner = address(0);
    }
}